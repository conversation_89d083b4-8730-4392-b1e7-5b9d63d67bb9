<div class="login-container">
  <div class="login-card">
    <div class="login-header">
      <h1 class="login-title">Welcome Back</h1>
      <p class="login-subtitle">Sign in to your account</p>
    </div>

    <form [formGroup]="loginForm" (ngSubmit)="onLogin()" class="login-form">
      <mat-form-field appearance="outline" class="form-field">
        <mat-label>Email</mat-label>
        <input
          matInput
          type="email"
          formControlName="email"
          placeholder="Enter your email"
        />
        <mat-error *ngIf="emailControl?.hasError('required')">
          Email is required
        </mat-error>
        <mat-error *ngIf="emailControl?.hasError('email')">
          Please enter a valid email
        </mat-error>
      </mat-form-field>

      <mat-form-field appearance="outline" class="form-field">
        <mat-label>Password</mat-label>
        <input
          matInput
          [type]="hidePassword ? 'password' : 'text'"
          formControlName="password"
          placeholder="Enter your password"
        />
        <button
          mat-icon-button
          matSuffix
          type="button"
          (click)="togglePasswordVisibility()"
          [attr.aria-label]="'Hide password'"
          [attr.aria-pressed]="hidePassword"
        >
          <mat-icon>{{
            hidePassword ? "visibility_off" : "visibility"
          }}</mat-icon>
        </button>
        <mat-error *ngIf="passwordControl?.hasError('required')">
          Password is required
        </mat-error>
        <mat-error *ngIf="passwordControl?.hasError('minlength')">
          Password must be at least 6 characters long
        </mat-error>
      </mat-form-field>

      <!-- <div class="form-options">
        <mat-checkbox formControlName="rememberMe" class="remember-checkbox">
          Remember me
        </mat-checkbox>
        <a href="#" class="forgot-password">Forgot password?</a>
      </div> -->

      <button
        mat-raised-button
        type="submit"
        class="login-button"
        [disabled]="loginForm.invalid || isLoading"
      >
        <mat-spinner
          *ngIf="isLoading"
          diameter="20"
          class="spinner"
        ></mat-spinner>
        <span *ngIf="!isLoading">Sign In</span>
      </button>
    </form>

    <div class="login-footer">
      <div class="forgot-password-container">
        <button
          mat-button
          type="button"
          class="forgot-password"
          (click)="onForgotPassword()"
        >
          Forgot password?
        </button>
      </div>
    </div>
  </div>
</div>
