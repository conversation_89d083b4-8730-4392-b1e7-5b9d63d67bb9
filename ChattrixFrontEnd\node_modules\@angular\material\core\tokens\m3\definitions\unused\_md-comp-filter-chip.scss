//
// Design system display name: Material 3
// Design system version: v0.161
//

@use 'sass:map';

@use './md-sys-color';

@use './md-sys-elevation';

@use './md-sys-shape';

@use './md-sys-state';

@use './md-sys-typescale';

$_default: (
  'md-sys-color': md-sys-color.values-light(),
  'md-sys-elevation': md-sys-elevation.values(),
  'md-sys-shape': md-sys-shape.values(),
  'md-sys-state': md-sys-state.values(),
  'md-sys-typescale': md-sys-typescale.values(),
);

@function values($deps: $_default, $exclude-hardcoded-values: false) {
  @return (
    'container-height': if($exclude-hardcoded-values, null, 32px),
    'container-shape': map.get($deps, 'md-sys-shape', 'corner-small'),
    'container-surface-tint-layer-color':
      map.get($deps, 'md-sys-color', 'surface-tint'),
    'disabled-label-text-color': map.get($deps, 'md-sys-color', 'on-surface'),
    'disabled-label-text-opacity': if($exclude-hardcoded-values, null, 0.38),
    'dragged-container-elevation': map.get($deps, 'md-sys-elevation', 'level4'),
    'elevated-container-elevation': map.get($deps, 'md-sys-elevation', 'level1'),
    'elevated-container-shadow-color': map.get($deps, 'md-sys-color', 'shadow'),
    'elevated-disabled-container-color':
      map.get($deps, 'md-sys-color', 'on-surface'),
    'elevated-disabled-container-elevation':
      map.get($deps, 'md-sys-elevation', 'level0'),
    'elevated-disabled-container-opacity':
      if($exclude-hardcoded-values, null, 0.12),
    'elevated-focus-container-elevation':
      map.get($deps, 'md-sys-elevation', 'level1'),
    'elevated-hover-container-elevation':
      map.get($deps, 'md-sys-elevation', 'level2'),
    'elevated-pressed-container-elevation':
      map.get($deps, 'md-sys-elevation', 'level1'),
    'elevated-selected-container-color':
      map.get($deps, 'md-sys-color', 'secondary-container'),
    'elevated-unselected-container-color':
      map.get($deps, 'md-sys-color', 'surface'),
    'flat-container-elevation': map.get($deps, 'md-sys-elevation', 'level0'),
    'flat-disabled-selected-container-color':
      map.get($deps, 'md-sys-color', 'on-surface'),
    'flat-disabled-selected-container-opacity':
      if($exclude-hardcoded-values, null, 0.12),
    'flat-disabled-unselected-outline-color':
      map.get($deps, 'md-sys-color', 'on-surface'),
    'flat-disabled-unselected-outline-opacity':
      if($exclude-hardcoded-values, null, 0.12),
    'flat-selected-container-color':
      map.get($deps, 'md-sys-color', 'secondary-container'),
    'flat-selected-focus-container-elevation':
      map.get($deps, 'md-sys-elevation', 'level0'),
    'flat-selected-hover-container-elevation':
      map.get($deps, 'md-sys-elevation', 'level1'),
    'flat-selected-outline-width': if($exclude-hardcoded-values, null, 0),
    'flat-selected-pressed-container-elevation':
      map.get($deps, 'md-sys-elevation', 'level0'),
    'flat-unselected-focus-container-elevation':
      map.get($deps, 'md-sys-elevation', 'level0'),
    'flat-unselected-focus-outline-color':
      map.get($deps, 'md-sys-color', 'on-surface-variant'),
    'flat-unselected-hover-container-elevation':
      map.get($deps, 'md-sys-elevation', 'level0'),
    'flat-unselected-outline-color': map.get($deps, 'md-sys-color', 'outline'),
    'flat-unselected-outline-width': if($exclude-hardcoded-values, null, 1px),
    'flat-unselected-pressed-container-elevation':
      map.get($deps, 'md-sys-elevation', 'level0'),
    'label-text-font': map.get($deps, 'md-sys-typescale', 'label-large-font'),
    'label-text-line-height':
      map.get($deps, 'md-sys-typescale', 'label-large-line-height'),
    'label-text-size': map.get($deps, 'md-sys-typescale', 'label-large-size'),
    'label-text-tracking':
      map.get($deps, 'md-sys-typescale', 'label-large-tracking'),
    // Warning: risk of reduced fidelity from using this composite typography token.
    // Tokens md.comp.filter-chip.label-text.tracking cannot be represented in the "font"
    // property shorthand. Consider using the discrete properties instead.
    'label-text-type':
      if(
        $exclude-hardcoded-values,
        null,
          map.get($deps, 'md-sys-typescale', 'label-large-weight')
          map.get($deps, 'md-sys-typescale', 'label-large-size') #{'/'} map.get(
            $deps,
            'md-sys-typescale',
            'label-large-line-height'
          ) map.get($deps, 'md-sys-typescale', 'label-large-font')
      ),
    'label-text-weight':
      map.get($deps, 'md-sys-typescale', 'label-large-weight'),
    'selected-dragged-label-text-color':
      map.get($deps, 'md-sys-color', 'on-secondary-container'),
    'selected-dragged-state-layer-color':
      map.get($deps, 'md-sys-color', 'on-secondary-container'),
    'selected-dragged-state-layer-opacity':
      map.get($deps, 'md-sys-state', 'dragged-state-layer-opacity'),
    'selected-focus-label-text-color':
      map.get($deps, 'md-sys-color', 'on-secondary-container'),
    'selected-focus-state-layer-color':
      map.get($deps, 'md-sys-color', 'on-secondary-container'),
    'selected-focus-state-layer-opacity':
      map.get($deps, 'md-sys-state', 'focus-state-layer-opacity'),
    'selected-hover-label-text-color':
      map.get($deps, 'md-sys-color', 'on-secondary-container'),
    'selected-hover-state-layer-color':
      map.get($deps, 'md-sys-color', 'on-secondary-container'),
    'selected-hover-state-layer-opacity':
      map.get($deps, 'md-sys-state', 'hover-state-layer-opacity'),
    'selected-label-text-color':
      map.get($deps, 'md-sys-color', 'on-secondary-container'),
    'selected-pressed-label-text-color':
      map.get($deps, 'md-sys-color', 'on-secondary-container'),
    'selected-pressed-state-layer-color':
      map.get($deps, 'md-sys-color', 'on-surface-variant'),
    'selected-pressed-state-layer-opacity':
      map.get($deps, 'md-sys-state', 'pressed-state-layer-opacity'),
    'unselected-dragged-label-text-color':
      map.get($deps, 'md-sys-color', 'on-surface-variant'),
    'unselected-dragged-state-layer-color':
      map.get($deps, 'md-sys-color', 'on-surface-variant'),
    'unselected-dragged-state-layer-opacity':
      map.get($deps, 'md-sys-state', 'dragged-state-layer-opacity'),
    'unselected-focus-label-text-color':
      map.get($deps, 'md-sys-color', 'on-surface-variant'),
    'unselected-focus-state-layer-color':
      map.get($deps, 'md-sys-color', 'on-surface-variant'),
    'unselected-focus-state-layer-opacity':
      map.get($deps, 'md-sys-state', 'focus-state-layer-opacity'),
    'unselected-hover-label-text-color':
      map.get($deps, 'md-sys-color', 'on-surface-variant'),
    'unselected-hover-state-layer-color':
      map.get($deps, 'md-sys-color', 'on-surface-variant'),
    'unselected-hover-state-layer-opacity':
      map.get($deps, 'md-sys-state', 'hover-state-layer-opacity'),
    'unselected-label-text-color':
      map.get($deps, 'md-sys-color', 'on-surface-variant'),
    'unselected-pressed-label-text-color':
      map.get($deps, 'md-sys-color', 'on-surface-variant'),
    'unselected-pressed-state-layer-color':
      map.get($deps, 'md-sys-color', 'on-secondary-container'),
    'unselected-pressed-state-layer-opacity':
      map.get($deps, 'md-sys-state', 'pressed-state-layer-opacity'),
    'with-icon-icon-size': if($exclude-hardcoded-values, null, 18px),
    'with-leading-icon-disabled-leading-icon-color':
      map.get($deps, 'md-sys-color', 'on-surface'),
    'with-leading-icon-disabled-leading-icon-opacity':
      if($exclude-hardcoded-values, null, 0.38),
    'with-leading-icon-selected-dragged-leading-icon-color':
      map.get($deps, 'md-sys-color', 'on-secondary-container'),
    'with-leading-icon-selected-focus-leading-icon-color':
      map.get($deps, 'md-sys-color', 'on-secondary-container'),
    'with-leading-icon-selected-hover-leading-icon-color':
      map.get($deps, 'md-sys-color', 'on-secondary-container'),
    'with-leading-icon-selected-leading-icon-color':
      map.get($deps, 'md-sys-color', 'on-secondary-container'),
    'with-leading-icon-selected-pressed-leading-icon-color':
      map.get($deps, 'md-sys-color', 'on-secondary-container'),
    'with-leading-icon-unselected-dragged-leading-icon-color':
      map.get($deps, 'md-sys-color', 'primary'),
    'with-leading-icon-unselected-focus-leading-icon-color':
      map.get($deps, 'md-sys-color', 'primary'),
    'with-leading-icon-unselected-hover-leading-icon-color':
      map.get($deps, 'md-sys-color', 'primary'),
    'with-leading-icon-unselected-leading-icon-color':
      map.get($deps, 'md-sys-color', 'primary'),
    'with-leading-icon-unselected-pressed-leading-icon-color':
      map.get($deps, 'md-sys-color', 'primary'),
    'with-trailing-icon-disabled-trailing-icon-color':
      map.get($deps, 'md-sys-color', 'on-surface'),
    'with-trailing-icon-disabled-trailing-icon-opacity':
      if($exclude-hardcoded-values, null, 0.38),
    'with-trailing-icon-selected-dragged-trailing-icon-color':
      map.get($deps, 'md-sys-color', 'on-secondary-container'),
    'with-trailing-icon-selected-focus-trailing-icon-color':
      map.get($deps, 'md-sys-color', 'on-secondary-container'),
    'with-trailing-icon-selected-hover-trailing-icon-color':
      map.get($deps, 'md-sys-color', 'on-secondary-container'),
    'with-trailing-icon-selected-pressed-trailing-icon-color':
      map.get($deps, 'md-sys-color', 'on-secondary-container'),
    'with-trailing-icon-selected-trailing-icon-color':
      map.get($deps, 'md-sys-color', 'on-secondary-container'),
    'with-trailing-icon-unselected-dragged-trailing-icon-color':
      map.get($deps, 'md-sys-color', 'on-surface-variant'),
    'with-trailing-icon-unselected-focus-trailing-icon-color':
      map.get($deps, 'md-sys-color', 'on-surface-variant'),
    'with-trailing-icon-unselected-hover-trailing-icon-color':
      map.get($deps, 'md-sys-color', 'on-surface-variant'),
    'with-trailing-icon-unselected-pressed-trailing-icon-color':
      map.get($deps, 'md-sys-color', 'on-surface-variant'),
    'with-trailing-icon-unselected-trailing-icon-color':
      map.get($deps, 'md-sys-color', 'on-surface-variant')
  );
}
