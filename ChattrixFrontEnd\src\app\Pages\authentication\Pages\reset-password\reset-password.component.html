<div class="auth-container">
  <div class="auth-card">
    <!-- Header Section -->
    <div class="auth-header">
      <h1 class="auth-title">Reset Password</h1>
      <p class="auth-subtitle">
        Create a new secure password for your account.
      </p>
    </div>

    <!-- Reset Password Form -->
    <form
      [formGroup]="resetPasswordForm"
      (ngSubmit)="onResetPassword()"
      class="auth-form"
    >
      <!-- New Password Field -->
      <mat-form-field appearance="outline" class="form-field">
        <mat-label>New Password</mat-label>
        <input
          matInput
          [type]="hidePassword ? 'password' : 'text'"
          formControlName="newPassword"
          placeholder="Create a strong password"
          autocomplete="new-password"
        />
        <button
          mat-icon-button
          matSuffix
          type="button"
          (click)="togglePasswordVisibility()"
          [attr.aria-label]="'Hide password'"
          [attr.aria-pressed]="hidePassword"
          class="auth-icon-button"
        >
          <mat-icon>{{
            hidePassword ? "visibility_off" : "visibility"
          }}</mat-icon>
        </button>
        <mat-error *ngIf="newPasswordControl?.hasError('required')">
          Password is required
        </mat-error>
        <mat-error *ngIf="newPasswordControl?.hasError('minlength')">
          Password must be at least 8 characters long
        </mat-error>
        <mat-error *ngIf="newPasswordControl?.hasError('passwordStrength')">
          Password must contain uppercase, lowercase, number, and special
          character
        </mat-error>
      </mat-form-field>

      <!-- Confirm Password Field -->
      <mat-form-field appearance="outline" class="form-field">
        <mat-label>Confirm New Password</mat-label>
        <input
          matInput
          [type]="hideConfirmPassword ? 'password' : 'text'"
          formControlName="confirmPassword"
          placeholder="Confirm your new password"
          autocomplete="new-password"
        />
        <button
          mat-icon-button
          matSuffix
          type="button"
          (click)="toggleConfirmPasswordVisibility()"
          [attr.aria-label]="'Hide password'"
          [attr.aria-pressed]="hideConfirmPassword"
          class="auth-icon-button"
        >
          <mat-icon>{{
            hideConfirmPassword ? "visibility_off" : "visibility"
          }}</mat-icon>
        </button>
        <mat-error *ngIf="confirmPasswordControl?.hasError('required')">
          Please confirm your password
        </mat-error>
        <mat-error
          *ngIf="
            resetPasswordForm.hasError('passwordMismatch') &&
            confirmPasswordControl?.touched
          "
        >
          Passwords do not match
        </mat-error>
      </mat-form-field>

      <!-- Password Strength Indicator -->
      <div
        class="password-strength"
        *ngIf="
          newPasswordControl?.hasError('passwordStrength') &&
          newPasswordControl?.touched
        "
      >
        <h4>Password must contain:</h4>
        <div class="strength-requirement">
          <div
            class="requirement-icon"
            [class.met]="
              newPasswordControl?.errors?.['passwordStrength']?.hasUpperCase
            "
            [class.unmet]="
              !newPasswordControl?.errors?.['passwordStrength']?.hasUpperCase
            "
          >
            ✓
          </div>
          <span
            class="requirement-text"
            [class.met]="
              newPasswordControl?.errors?.['passwordStrength']?.hasUpperCase
            "
            [class.unmet]="
              !newPasswordControl?.errors?.['passwordStrength']?.hasUpperCase
            "
          >
            At least one uppercase letter
          </span>
        </div>
        <div class="strength-requirement">
          <div
            class="requirement-icon"
            [class.met]="
              newPasswordControl?.errors?.['passwordStrength']?.hasLowerCase
            "
            [class.unmet]="
              !newPasswordControl?.errors?.['passwordStrength']?.hasLowerCase
            "
          >
            ✓
          </div>
          <span
            class="requirement-text"
            [class.met]="
              newPasswordControl?.errors?.['passwordStrength']?.hasLowerCase
            "
            [class.unmet]="
              !newPasswordControl?.errors?.['passwordStrength']?.hasLowerCase
            "
          >
            At least one lowercase letter
          </span>
        </div>
        <div class="strength-requirement">
          <div
            class="requirement-icon"
            [class.met]="
              newPasswordControl?.errors?.['passwordStrength']?.hasNumeric
            "
            [class.unmet]="
              !newPasswordControl?.errors?.['passwordStrength']?.hasNumeric
            "
          >
            ✓
          </div>
          <span
            class="requirement-text"
            [class.met]="
              newPasswordControl?.errors?.['passwordStrength']?.hasNumeric
            "
            [class.unmet]="
              !newPasswordControl?.errors?.['passwordStrength']?.hasNumeric
            "
          >
            At least one number
          </span>
        </div>
        <div class="strength-requirement">
          <div
            class="requirement-icon"
            [class.met]="
              newPasswordControl?.errors?.['passwordStrength']?.hasSpecialChar
            "
            [class.unmet]="
              !newPasswordControl?.errors?.['passwordStrength']?.hasSpecialChar
            "
          >
            ✓
          </div>
          <span
            class="requirement-text"
            [class.met]="
              newPasswordControl?.errors?.['passwordStrength']?.hasSpecialChar
            "
            [class.unmet]="
              !newPasswordControl?.errors?.['passwordStrength']?.hasSpecialChar
            "
          >
            At least one special character
          </span>
        </div>
      </div>

      <!-- Reset Password Button -->
      <button
        mat-raised-button
        type="submit"
        class="auth-button full-width"
        [disabled]="resetPasswordForm.invalid || isLoading"
      >
        <div class="button-content">
          <mat-spinner
            *ngIf="isLoading"
            diameter="20"
            class="button-spinner"
          ></mat-spinner>
          <span [class.hidden]="isLoading">Reset Password</span>
        </div>
      </button>
    </form>

    <!-- Footer Section -->
    <div class="auth-footer">
      <div class="back-to-login">
        <button mat-button (click)="onBackToLogin()" class="back-button">
          <mat-icon>arrow_back</mat-icon>
          Back to Login
        </button>
      </div>
    </div>
  </div>
</div>
