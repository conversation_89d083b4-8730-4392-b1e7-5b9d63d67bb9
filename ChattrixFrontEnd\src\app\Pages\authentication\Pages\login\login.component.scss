.login-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #f5f5f5 0%, #e8e8e8 100%);
  padding: 20px;
}

.login-card {
  background: #ffffff;
  border-radius: 12px;
  padding: 48px 40px;
  width: 100%;
  max-width: 400px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(0, 0, 0, 0.05);
  // transition: all 0.3s ease;
}

.login-card:hover {
  box-shadow: 0 12px 48px rgba(0, 0, 0, 0.15);
  // transform: translateY(-2px);
}

.login-header {
  text-align: center;
  margin-bottom: 32px;
}

.login-title {
  font-size: 28px;
  font-weight: 600;
  color: #1a1a1a;
  margin: 0 0 8px 0;
  letter-spacing: -0.5px;
}

.login-subtitle {
  font-size: 14px;
  color: #666;
  margin: 0;
  font-weight: 400;
}

.login-form {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.form-field {
  width: 100%;
}

.form-field ::ng-deep .mat-mdc-form-field-outline {
  color: #e0e0e0;
}

.form-field ::ng-deep .mat-mdc-form-field-focus-overlay {
  background-color: transparent;
}

.form-field ::ng-deep .mat-mdc-form-field-outline-thick {
  color: #1a1a1a;
}

.form-field ::ng-deep .mat-mdc-floating-label {
  color: #666;
}

.form-field ::ng-deep .mat-mdc-floating-label.mdc-floating-label--float-above {
  color: #1a1a1a;
}

.form-field ::ng-deep .mat-mdc-input-element {
  color: #1a1a1a;
}

.form-field ::ng-deep .mat-mdc-input-element::placeholder {
  color: #999;
}

.form-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: -8px 0;
}

.remember-checkbox ::ng-deep .mat-mdc-checkbox-frame {
  border-color: #ccc;
}

.remember-checkbox ::ng-deep .mat-mdc-checkbox-checkmark {
  color: #fff;
}

.remember-checkbox ::ng-deep .mat-mdc-checkbox-mixedmark {
  color: #fff;
}

.remember-checkbox
  ::ng-deep
  .mdc-checkbox__native-control:enabled:checked
  ~ .mdc-checkbox__background {
  background-color: #1a1a1a;
  border-color: #1a1a1a;
}

.remember-checkbox ::ng-deep .mat-mdc-checkbox-touch-target {
  width: 40px;
  height: 40px;
}

.forgot-password {
  color: #666;
  text-decoration: none;
  font-size: 14px;
  transition: color 0.2s ease;
}

.forgot-password:hover {
  color: #1a1a1a;
  text-decoration: underline;
}

.login-button {
  background-color: #1a1a1a;
  color: white;
  border: none;
  border-radius: 8px;
  padding: 16px;
  font-size: 16px;
  font-weight: 500;
  letter-spacing: 0.5px;
  transition: all 0.2s ease;
  margin-top: 8px;
  position: relative;
  min-height: 56px;
}

.login-button:hover:not(:disabled) {
  background-color: #2a2a2a;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.login-button:disabled {
  background-color: #ccc;
  color: #999;
  cursor: not-allowed;
}

.spinner {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
}

.spinner ::ng-deep circle {
  stroke: white;
}

.login-footer {
  text-align: center;
  margin-top: 32px;
  padding-top: 24px;
  border-top: 1px solid #f0f0f0;
}

.login-footer p {
  color: #666;
  font-size: 14px;
  margin: 0;
}

.signup-link {
  color: #1a1a1a;
  text-decoration: none;
  font-weight: 500;
  transition: color 0.2s ease;
}

.signup-link:hover {
  color: #333;
  text-decoration: underline;
}

@media (max-width: 480px) {
  .login-container {
    padding: 16px;
  }

  .login-card {
    padding: 32px 24px;
  }

  .login-title {
    font-size: 24px;
  }
}
