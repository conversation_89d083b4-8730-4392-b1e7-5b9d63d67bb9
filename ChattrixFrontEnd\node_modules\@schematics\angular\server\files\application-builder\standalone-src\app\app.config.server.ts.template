import { mergeApplicationConfig, ApplicationConfig } from '@angular/core';
import { provideServerRendering } from '@angular/platform-server';<% if(serverRouting) { %>
import { provideServerRouting } from '@angular/ssr';<% } %>
import { appConfig } from './app.config';<% if(serverRouting) { %>
import { serverRoutes } from './app.routes.server';<% } %>

const serverConfig: ApplicationConfig = {
  providers: [
    provideServerRendering(),<% if(serverRouting) { %>
    provideServerRouting(serverRoutes)<% } %>
  ]
};

export const config = mergeApplicationConfig(appConfig, serverConfig);
