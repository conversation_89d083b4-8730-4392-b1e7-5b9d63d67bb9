//
// Design system display name: Material 3
// Design system version: v0.161
//

@use 'sass:map';

@use './md-sys-color';

@use './md-sys-elevation';

@use './md-sys-shape';

@use './md-sys-state';

@use './md-sys-typescale';

$_default: (
  'md-sys-color': md-sys-color.values-light(),
  'md-sys-elevation': md-sys-elevation.values(),
  'md-sys-shape': md-sys-shape.values(),
  'md-sys-state': md-sys-state.values(),
  'md-sys-typescale': md-sys-typescale.values(),
);

@function values($deps: $_default, $exclude-hardcoded-values: false) {
  @return (
    'detached-container-shape': map.get($deps, 'md-sys-shape', 'corner-large'),
    'docked-action-focus-label-text-color':
      map.get($deps, 'md-sys-color', 'primary'),
    'docked-action-focus-state-layer-color':
      map.get($deps, 'md-sys-color', 'primary'),
    'docked-action-focus-state-layer-opacity':
      map.get($deps, 'md-sys-state', 'focus-state-layer-opacity'),
    'docked-action-hover-label-text-color':
      map.get($deps, 'md-sys-color', 'primary'),
    'docked-action-hover-state-layer-color':
      map.get($deps, 'md-sys-color', 'primary'),
    'docked-action-hover-state-layer-opacity':
      map.get($deps, 'md-sys-state', 'hover-state-layer-opacity'),
    'docked-action-pressed-label-text-color':
      map.get($deps, 'md-sys-color', 'primary'),
    'docked-action-pressed-state-layer-color':
      map.get($deps, 'md-sys-color', 'primary'),
    'docked-action-pressed-state-layer-opacity':
      map.get($deps, 'md-sys-state', 'pressed-state-layer-opacity'),
    'docked-container-color': map.get($deps, 'md-sys-color', 'surface'),
    'docked-container-height': if($exclude-hardcoded-values, null, 100%),
    'docked-container-shape': map.get($deps, 'md-sys-shape', 'corner-none'),
    'docked-container-surface-tint-layer-color':
      map.get($deps, 'md-sys-color', 'surface-tint'),
    'docked-container-width': if($exclude-hardcoded-values, null, 256px),
    'docked-divider-color': map.get($deps, 'md-sys-color', 'outline'),
    'docked-headline-color':
      map.get($deps, 'md-sys-color', 'on-surface-variant'),
    'docked-headline-font':
      map.get($deps, 'md-sys-typescale', 'title-large-font'),
    'docked-headline-line-height':
      map.get($deps, 'md-sys-typescale', 'title-large-line-height'),
    'docked-headline-size':
      map.get($deps, 'md-sys-typescale', 'title-large-size'),
    'docked-headline-tracking':
      map.get($deps, 'md-sys-typescale', 'title-large-tracking'),
    // Warning: risk of reduced fidelity from using this composite typography token.
    // Tokens md.comp.sheet.side.docked.headline.tracking cannot be represented in the "font"
    // property shorthand. Consider using the discrete properties instead.
    'docked-headline-type':
      if(
        $exclude-hardcoded-values,
        null,
          map.get($deps, 'md-sys-typescale', 'title-large-weight')
          map.get($deps, 'md-sys-typescale', 'title-large-size') #{'/'} map.get(
            $deps,
            'md-sys-typescale',
            'title-large-line-height'
          ) map.get($deps, 'md-sys-typescale', 'title-large-font')
      ),
    'docked-headline-weight':
      map.get($deps, 'md-sys-typescale', 'title-large-weight'),
    'docked-modal-container-elevation':
      map.get($deps, 'md-sys-elevation', 'level1'),
    'docked-modal-container-shape':
      map.get($deps, 'md-sys-shape', 'corner-large-start'),
    'docked-standard-container-elevation':
      map.get($deps, 'md-sys-elevation', 'level0')
  );
}
