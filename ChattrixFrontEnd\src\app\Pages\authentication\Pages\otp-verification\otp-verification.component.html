<div class="auth-container">
  <div class="auth-card">
    <!-- Header Section -->
    <div class="auth-header">
      <h1 class="auth-title">Verify Your Account</h1>
      <p class="auth-subtitle">
        We've sent a 5-digit verification code to your email address. Please
        enter it below to complete your verification.
      </p>
    </div>

    <!-- OTP Form -->
    <form [formGroup]="otpForm" (ngSubmit)="onVerifyOtp()" class="auth-form">
      <!-- OTP Input Field -->
      <div class="otp-container">
        <mat-form-field class="form-field" appearance="outline">
          <mat-label>Enter 5-digit verification code</mat-label>
          <input
            matInput
            formControlName="otpCode"
            type="text"
            maxlength="5"
            placeholder="12345"
            autocomplete="one-time-code"
            inputmode="numeric"
            pattern="[0-9]*"
            class="otp-input-single"
            (input)="onOtpInput($event)"
            (paste)="onOtpPaste($event)"
          />
          <mat-error *ngIf="otpForm.get('otpCode')?.hasError('required')">
            Verification code is required
          </mat-error>
          <mat-error *ngIf="otpForm.get('otpCode')?.hasError('pattern')">
            Please enter a valid 5-digit numeric code
          </mat-error>
          <mat-error
            *ngIf="
              otpForm.get('otpCode')?.hasError('minlength') ||
              otpForm.get('otpCode')?.hasError('maxlength')
            "
          >
            Verification code must be exactly 5 digits
          </mat-error>
        </mat-form-field>
      </div>

      <!-- Verify Button -->
      <button
        mat-raised-button
        type="submit"
        class="auth-button full-width"
        [disabled]="otpForm.invalid || isLoading"
      >
        <div class="button-content">
          <mat-spinner
            *ngIf="isLoading"
            diameter="20"
            class="button-spinner"
          ></mat-spinner>
          <span [class.hidden]="isLoading">Verify Code</span>
        </div>
      </button>

      <!-- Resend Section -->
      <div class="resend-section">
        <p class="resend-text">Didn't receive the code?</p>

        <button
          *ngIf="!canResend"
          mat-button
          type="button"
          class="resend-button disabled"
          disabled
        >
          <mat-icon>schedule</mat-icon>
          Resend in {{ timerDisplay }}
        </button>

        <button
          *ngIf="canResend"
          mat-button
          type="button"
          class="resend-button"
          (click)="onResendOtp()"
          [disabled]="isResending"
        >
          <div class="button-content">
            <mat-spinner
              *ngIf="isResending"
              diameter="16"
              class="resend-spinner"
            ></mat-spinner>
            <mat-icon *ngIf="!isResending" [class.hidden]="isResending"
              >refresh</mat-icon
            >
            <span [class.hidden]="isResending">Resend Code</span>
          </div>
        </button>
      </div>
    </form>

    <!-- Footer Section -->
    <div class="auth-footer">
      <div class="back-to-login">
        <button mat-button (click)="onBackToLogin()" class="back-button">
          <mat-icon>arrow_back</mat-icon>
          Back to Login
        </button>
      </div>
    </div>
  </div>
</div>
