/* Chattrix Global Styles with Material UI Integration */
@import url("https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap");
@import url("https://fonts.googleapis.com/css2?family=SF+Pro+Display:wght@400;500;600;700&display=swap");
@import url("https://fonts.googleapis.com/css2?family=SF+Pro+Text:wght@400;500;600;700&display=swap");
@import "@angular/material/prebuilt-themes/indigo-pink.css";

/* CSS Variables for Chattrix Color Palette - Black & White Minimalist */
:root {
  /* Primary Colors - Black & White Theme */
  --primary-dark: #000000;
  --primary-darker: #000000;
  --secondary-dark: #1a1a1a;
  --accent-primary: #000000;
  --accent-primary-hover: #333333;
  --accent-secondary: #ffffff;

  /* Text Colors */
  --text-primary: #ffffff;
  --text-secondary: #cccccc;
  --text-muted: #999999;
  --text-disabled: #666666;

  /* Background Colors */
  --bg-primary: #90747403;
  --bg-secondary: #625b5b;
  --bg-tertiary: #333333;
  --bg-card: #777171;
  --bg-input: #333333;
  --bg-hover: #404040;
  --bg-main-content: #f8f9fa; /* Light grayish-white for main content area */

  /* Border Colors */
  --border-primary: #333333;
  --border-secondary: #666666;
  --border-focus: #ffffff;

  /* Status Colors */
  --success: #000000;
  --error: #000000;
  --warning: #000000;
  --info: #000000;

  /* Material UI Overrides */
  --mdc-theme-primary: #000000;
  --mdc-theme-secondary: #ffffff;
  --mdc-theme-surface: #1a1a1a;
  --mdc-theme-background: #000000;
  --mdc-theme-on-primary: #ffffff;
  --mdc-theme-on-secondary: #000000;
  --mdc-theme-on-surface: #ffffff;
  --mdc-theme-on-background: #ffffff;

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md:
    0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg:
    0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl:
    0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

  /* Border Radius */
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;

  /* Spacing */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  --spacing-2xl: 3rem;

  /* Typography */
  --font-family:
    "Inter SemiBold", "SF Pro Display Medium", "SF Pro Text", -apple-system,
    BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 1.875rem;
  --font-size-4xl: 2.25rem;

  /* Line Heights */
  --line-height-tight: 1.25;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.75;

  /* Transitions */
  --transition-fast: 150ms ease-in-out;
  --transition-normal: 250ms ease-in-out;
  --transition-slow: 350ms ease-in-out;
}

/* Global Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  font-size: 16px;
  scroll-behavior: smooth;
}

body {
  font-family: var(--font-family);
  font-size: var(--font-size-base);
  line-height: var(--line-height-normal);
  color: var(--text-primary);
  background-color: var(--bg-primary);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  overflow-x: hidden;
}

/* Typography Classes */
.text-primary {
  color: var(--text-primary);
}
.text-secondary {
  color: var(--text-secondary);
}
.text-muted {
  color: var(--text-muted);
}
.text-disabled {
  color: var(--text-disabled);
}
.text-success {
  color: var(--success);
}
.text-error {
  color: var(--error);
}
.text-warning {
  color: var(--warning);
}
.text-info {
  color: var(--info);
}

/* Font Weight Classes */
.font-light {
  font-weight: 300;
}
.font-normal {
  font-weight: 400;
}
.font-medium {
  font-weight: 500;
}
.font-semibold {
  font-weight: 600;
}
.font-bold {
  font-weight: 700;
}

/* Font Size Classes */
.text-xs {
  font-size: var(--font-size-xs);
}
.text-sm {
  font-size: var(--font-size-sm);
}
.text-base {
  font-size: var(--font-size-base);
}
.text-lg {
  font-size: var(--font-size-lg);
}
.text-xl {
  font-size: var(--font-size-xl);
}
.text-2xl {
  font-size: var(--font-size-2xl);
}
.text-3xl {
  font-size: var(--font-size-3xl);
}
.text-4xl {
  font-size: var(--font-size-4xl);
}

.mdc-text-field--outlined:not(.mdc-text-field--disabled)
  .mdc-notched-outline__leading,
.mdc-text-field--outlined:not(.mdc-text-field--disabled)
  .mdc-notched-outline__notch,
.mdc-text-field--outlined:not(.mdc-text-field--disabled)
  .mdc-notched-outline__trailing {
  border-color: var(--border-primary) !important;
}

/* Change the border hover color */
.mdc-text-field--outlined:not(.mdc-text-field--disabled):not(
    .mdc-text-field--focused
  ):hover
  .mdc-notched-outline
  .mdc-notched-outline__leading,
.mdc-text-field--outlined:not(.mdc-text-field--disabled):not(
    .mdc-text-field--focused
  ):hover
  .mdc-notched-outline
  .mdc-notched-outline__notch,
.mdc-text-field--outlined:not(.mdc-text-field--disabled):not(
    .mdc-text-field--focused
  ):hover
  .mdc-notched-outline
  .mdc-notched-outline__trailing {
  border-color: var(--border-secondary) !important;
}

/* Change the border focused color */
.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--focused
  .mdc-notched-outline__leading,
.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--focused
  .mdc-notched-outline__notch,
.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--focused
  .mdc-notched-outline__trailing {
  border-color: var(--border-focus) !important;
}

.mat-mdc-paginator-navigation-next,
.mat-mdc-paginator-navigation-previous,
.mat-mdc-paginator-navigation-first,
.mat-mdc-paginator-navigation-last {
  color: var(--text-primary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-sm);
  background: var(--bg-secondary);
  width: 36px;
  height: 36px;
  margin: 0 var(--spacing-xs);

  &:hover:not([disabled]) {
    background: var(--bg-hover);
    color: var(--text-primary);
    border-color: var(--border-secondary);
  }

  &[disabled] {
    color: var(--text-muted);
    background: var(--bg-tertiary);
    border-color: var(--border-primary);
    opacity: 0.6;
    cursor: not-allowed;
  }

  .mat-mdc-button-touch-target {
    width: 36px;
    height: 36px;
  }

  mat-icon {
    font-size: 18px;
    width: 18px;
    height: 18px;
  }
}

/* Material UI Component Overrides for Black & White Theme */
.mat-mdc-form-field {
  --mdc-filled-text-field-container-color: var(--bg-input);
  --mdc-filled-text-field-label-text-color: var(--text-secondary);
  --mdc-filled-text-field-input-text-color: var(--text-primary);
  --mdc-filled-text-field-active-indicator-color: var(--accent-primary);
  --mdc-filled-text-field-focus-active-indicator-color: var(--accent-primary);
  --mdc-filled-text-field-hover-active-indicator-color: var(--accent-secondary);

  /* Reduce form field height and spacing */
  .mdc-text-field {
    height: 48px;
  }

  .mat-mdc-form-field-wrapper {
    padding-bottom: 0;
  }

  .mat-mdc-form-field-subscript-wrapper {
    margin-top: 4px;
    min-height: 16px;
  }
}

.mat-mdc-button.mat-primary {
  --mdc-filled-button-container-color: var(--accent-primary);
  --mdc-filled-button-label-text-color: var(--text-primary);
}

.mat-mdc-button.mat-primary:hover {
  --mdc-filled-button-container-color: var(--accent-primary-hover);
}

.mat-mdc-card {
  border-radius: 10px;
  --mdc-elevated-card-container-color: var(--bg-card);
  --mdc-elevated-card-container-shadow: var(--shadow-lg);
}

/* Additional Material UI Component Overrides for Black & White Theme */
.mat-mdc-table {
  --mdc-data-table-container-color: var(--bg-card);
  --mdc-data-table-row-item-label-text-color: var(--text-primary);
  --mdc-data-table-header-row-item-label-text-color: var(--text-secondary);
}

.mat-mdc-paginator {
  --mdc-filled-button-container-color: var(--accent-primary);
  --mdc-filled-button-label-text-color: var(--text-primary);
}

.mat-mdc-select {
  --mdc-filled-text-field-container-color: var(--bg-input);
  --mdc-filled-text-field-label-text-color: var(--text-secondary);
  --mdc-filled-text-field-input-text-color: var(--text-primary);
}

.mat-mdc-dialog-container {
  --mdc-dialog-container-color: var(--bg-card);
  --mdc-dialog-supporting-text-color: var(--text-primary);
  --mdc-dialog-subhead-color: var(--text-secondary);
}

.mat-mdc-snack-bar-container {
  --mdc-snackbar-container-color: var(--bg-card);
  --mdc-snackbar-supporting-text-color: var(--text-primary);
}

.mat-mdc-menu-panel {
  --mdc-menu-container-color: var(--bg-card);
  --mdc-list-list-item-label-text-color: var(--text-primary);
}

/* Authentication Layout Styles */
.auth-container {
  min-height: 100vh;
  min-height: 100dvh; /* Dynamic viewport height for mobile browsers */
  display: flex;
  align-items: center;
  justify-content: center;
  background: #e8e8e8; /* Light grayish-white for refined minimalist design */
  padding: var(--spacing-sm);
  box-sizing: border-box;
}

mat-card.auth-card {
  border-radius: 20px;
}

.auth-card {
  width: 100%;
  max-width: 400px;
  background: #ffffff; /* White card background */
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-xl);
  border: 2px solid #dbd9d9;
  overflow: hidden;
}

.auth-header {
  text-align: center;
  padding: var(--spacing-md) var(--spacing-lg) var(--spacing-sm);
  background: #ffffff; /* Dark grayish-white background */
}

.auth-logo {
  width: 110px;
  height: 110px;
  margin: 0 auto var(--spacing-md);
  border-radius: 50%;
  overflow: hidden;
  border: 3px solid #ffffff; /* White border */
  box-shadow: 0 0 20px rgba(255, 255, 255, 0.3); /* White glow */
  display: flex;
  align-items: center;
  justify-content: center;
  background: #ffffff; /* White background for logo */
  margin-bottom: 20px;
}

.auth-logo img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.auth-title {
  font-size: var(--font-size-xl);
  font-weight: 600;
  color: #ffffff; /* White text on black card */
  margin-bottom: var(--spacing-xs);
  line-height: var(--line-height-tight);
}

.auth-subtitle {
  font-size: var(--font-size-sm);
  color: #cccccc; /* Light gray text on black card */
  margin-bottom: 0;
  line-height: var(--line-height-normal);
}

.auth-form {
  padding: var(--spacing-md) var(--spacing-lg);
  background: #ffffff; /* White form background */
}

.auth-footer {
  padding: var(--spacing-md) var(--spacing-lg);
  text-align: center;
  border-top: 1px solid #333333; /* Dark border */
  background: #ffffff; /* Black footer background */
}

/* Mobile-First Responsive Design Enhancements */
@media (max-width: 768px) {
  .auth-container {
    padding: var(--spacing-sm);
    min-height: 100vh;
    min-height: 100dvh; /* Dynamic viewport height for mobile browsers */
  }

  .auth-card {
    margin: 0;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
  }

  .auth-header {
    padding: var(--spacing-xl) var(--spacing-lg) var(--spacing-md);
  }

  .auth-form {
    padding: var(--spacing-lg);
  }

  .auth-footer {
    padding: var(--spacing-md) var(--spacing-lg);
  }
}

@media (max-width: 480px) {
  .auth-container {
    padding: var(--spacing-xs);
  }

  .auth-card {
    border-radius: var(--radius-md);
  }

  .auth-header {
    padding: var(--spacing-lg) var(--spacing-md) var(--spacing-sm);
  }

  .auth-form {
    padding: var(--spacing-md);
  }

  .auth-footer {
    padding: var(--spacing-sm) var(--spacing-md);
  }

  .auth-title {
    font-size: var(--font-size-xl);
    line-height: var(--line-height-tight);
  }

  .auth-subtitle {
    font-size: var(--font-size-xs);
    line-height: var(--line-height-normal);
  }
}

/* Touch-Friendly Interactions */
@media (hover: none) and (pointer: coarse) {
  /* Increase touch targets for mobile */
  .mat-mdc-button {
    min-height: 44px;
    min-width: 44px;
  }

  .mat-mdc-icon-button {
    width: 44px;
    height: 44px;
  }

  /* Remove hover effects on touch devices */
  .mat-mdc-button:hover {
    background-color: inherit;
  }

  /* Improve form field touch targets */
  .mat-mdc-form-field .mat-mdc-text-field-wrapper {
    min-height: 48px;
  }
}

/* High DPI Display Support */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .auth-logo img {
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
  }
}

/* Landscape Orientation on Mobile */
@media (max-height: 500px) and (orientation: landscape) {
  .auth-container {
    padding: var(--spacing-xs);
  }

  .auth-card {
    max-height: 90vh;
    overflow-y: auto;
  }

  .auth-header {
    padding: var(--spacing-md) var(--spacing-lg) var(--spacing-sm);
  }

  .auth-logo {
    width: 50px;
    height: 50px;
    margin-bottom: var(--spacing-sm);
  }

  .auth-title {
    font-size: var(--font-size-lg);
    margin-bottom: var(--spacing-xs);
  }

  .auth-subtitle {
    font-size: var(--font-size-xs);
    margin-bottom: var(--spacing-xs);
  }
}

/* Accessibility Improvements */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Dark Mode Support (if system preference changes) */
@media (prefers-color-scheme: dark) {
  /* Our app is already dark-themed, but we can add specific adjustments here if needed */
  :root {
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
    --shadow-md:
      0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2);
    --shadow-lg:
      0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.2);
    --shadow-xl:
      0 20px 25px -5px rgba(0, 0, 0, 0.3), 0 10px 10px -5px rgba(0, 0, 0, 0.2);
  }
}
