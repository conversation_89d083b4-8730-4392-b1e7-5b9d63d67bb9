/* 
 * Shared Authentication Theme - Fixed Design Independent of Global Theme
 * This file provides consistent styling for all authentication components
 * that remains constant regardless of user theme preferences (light/dark mode)
 */

/* Authentication Color Palette - Fixed Light Theme */
:root {
  /* Authentication-specific color variables */
  --auth-bg-container: linear-gradient(135deg, #f5f5f5 0%, #e8e8e8 100%);
  --auth-bg-card: #ffffff;
  --auth-text-primary: #1a1a1a;
  --auth-text-secondary: #666666;
  --auth-text-muted: #999999;
  --auth-border-default: #e0e0e0;
  --auth-border-hover: #999999;
  --auth-border-focus: #1a1a1a;
  --auth-button-bg: #1a1a1a;
  --auth-button-text: #ffffff;
  --auth-button-hover: #2a2a2a;
  --auth-button-disabled: #cccccc;
  --auth-shadow-card: 0 8px 32px rgba(0, 0, 0, 0.1);
  --auth-shadow-hover: 0 12px 48px rgba(0, 0, 0, 0.15);
}

/* Authentication Container Styles */
.auth-container {
  min-height: 100vh;
  min-height: 100dvh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #f5f5f5 0%, #e8e8e8 100%);
  padding: 20px;
  box-sizing: border-box;
}

/* Authentication Card Styles */
.auth-card {
  background: #ffffff;
  border-radius: 12px;
  padding: 48px 40px;
  width: 100%;
  max-width: 400px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 3px solid rgba(0, 0, 0, 0.05);
  transition: box-shadow 0.3s ease;
}

.auth-card:hover {
  box-shadow: var(--auth-shadow-hover);
}

/* Authentication Header Styles */
.auth-header {
  text-align: center;
  margin-bottom: 32px;
}

.auth-title {
  font-size: 28px;
  font-weight: 600;
  color: var(--auth-text-primary);
  margin: 0 0 8px 0;
  letter-spacing: -0.5px;
}

.auth-subtitle {
  font-size: 14px;
  color: var(--auth-text-secondary);
  margin: 0;
  font-weight: 400;
}

/* Authentication Form Styles */
.auth-form {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.form-field {
  width: 100%;
}

/* Material UI Form Field Overrides - Fixed Authentication Theme */
.form-field ::ng-deep .mat-mdc-form-field-outline {
  color: var(--auth-border-default) !important;
}

.form-field ::ng-deep .mat-mdc-form-field-focus-overlay {
  background-color: transparent !important;
}

.form-field ::ng-deep .mat-mdc-form-field-outline-thick {
  color: var(--auth-border-focus) !important;
}

/* Disable floating label animation - keep label always floated */
.form-field ::ng-deep .mat-mdc-floating-label {
  color: var(--auth-text-secondary) !important;
  transform: translateY(-50%) scale(0.75) !important;
  transition: none !important;
  top: 0 !important;
}

.form-field ::ng-deep .mat-mdc-floating-label.mdc-floating-label--float-above {
  color: var(--auth-text-primary) !important;
  transform: translateY(-50%) scale(0.75) !important;
  transition: none !important;
}

.form-field ::ng-deep .mat-mdc-input-element {
  color: var(--auth-text-primary) !important;
}

.form-field ::ng-deep .mat-mdc-input-element::placeholder {
  color: var(--auth-text-muted) !important;
}

/* Override browser autofill styling */
.form-field ::ng-deep .mat-mdc-input-element:-webkit-autofill,
.form-field ::ng-deep .mat-mdc-input-element:-webkit-autofill:hover,
.form-field ::ng-deep .mat-mdc-input-element:-webkit-autofill:focus,
.form-field ::ng-deep .mat-mdc-input-element:-webkit-autofill:active {
  -webkit-box-shadow: 0 0 0 30px var(--auth-bg-card) inset !important;
  -webkit-text-fill-color: var(--auth-text-primary) !important;
  background-color: var(--auth-bg-card) !important;
  transition: background-color 5000s ease-in-out 0s !important;
}

/* Firefox autofill override */
.form-field ::ng-deep .mat-mdc-input-element:-moz-autofill {
  background-color: var(--auth-bg-card) !important;
  color: var(--auth-text-primary) !important;
}

/* Material UI Outlined Text Field Overrides */
.form-field
  ::ng-deep
  .mdc-text-field--outlined:not(.mdc-text-field--disabled)
  .mdc-notched-outline__leading,
.form-field
  ::ng-deep
  .mdc-text-field--outlined:not(.mdc-text-field--disabled)
  .mdc-notched-outline__notch,
.form-field
  ::ng-deep
  .mdc-text-field--outlined:not(.mdc-text-field--disabled)
  .mdc-notched-outline__trailing {
  border-color: var(--auth-border-default) !important;
}

.form-field
  ::ng-deep
  .mdc-text-field--outlined:not(.mdc-text-field--disabled):hover
  .mdc-notched-outline
  .mdc-notched-outline__leading,
.form-field
  ::ng-deep
  .mdc-text-field--outlined:not(.mdc-text-field--disabled):hover
  .mdc-notched-outline
  .mdc-notched-outline__notch,
.form-field
  ::ng-deep
  .mdc-text-field--outlined:not(.mdc-text-field--disabled):hover
  .mdc-notched-outline
  .mdc-notched-outline__trailing {
  border-color: var(--auth-border-hover) !important;
}

.form-field
  ::ng-deep
  .mdc-text-field--outlined:not(
    .mdc-text-field--disabled
  ).mdc-text-field--focused
  .mdc-notched-outline__leading,
.form-field
  ::ng-deep
  .mdc-text-field--outlined:not(
    .mdc-text-field--disabled
  ).mdc-text-field--focused
  .mdc-notched-outline__notch,
.form-field
  ::ng-deep
  .mdc-text-field--outlined:not(
    .mdc-text-field--disabled
  ).mdc-text-field--focused
  .mdc-notched-outline__trailing {
  border-color: var(--auth-border-focus) !important;
  border-width: 2px !important;
}

/* Force the notched outline to always show the notch for the permanently floated label */
.form-field ::ng-deep .mdc-notched-outline__notch {
  border-top: none !important;
}

/* Ensure proper spacing for the permanently floated label */
.form-field ::ng-deep .mdc-text-field--outlined .mdc-text-field__input {
  padding-top: 16px !important;
  padding-bottom: 16px !important;
}

/* Additional autofill styling for better cross-browser support */
.form-field ::ng-deep .mat-mdc-input-element {
  background-color: transparent !important;
}

.form-field ::ng-deep .mat-mdc-input-element:autofill {
  background-color: var(--auth-bg-card) !important;
  -webkit-box-shadow: 0 0 0 1000px var(--auth-bg-card) inset !important;
  box-shadow: 0 0 0 1000px var(--auth-bg-card) inset !important;
}

/* Authentication Button Styles */
.auth-button {
  background-color: #1a1a1a;
  color: white;
  border: none;
  border-radius: 8px;
  padding: 16px;
  font-size: 16px;
  font-weight: 500;
  letter-spacing: 0.5px;
  transition: all 0.2s ease;
  margin-top: 8px;
  position: relative;
  min-height: 56px;
}

.auth-button:hover:not(:disabled) {
  background-color: #2a2a2a;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.auth-button:disabled {
  border: 1px solid rgb(225, 225, 225);
  background-color: #787878;
  color: #999;
  cursor: not-allowed;
}

/* Authentication Footer Styles */
.auth-footer {
  text-align: center;
  margin-top: 32px;
  padding-top: 24px;
  border-top: 1px solid #f0f0f0;
}

.auth-footer p {
  color: var(--auth-text-secondary);
  font-size: 14px;
  margin: 0;
}

.auth-link {
  color: var(--auth-text-primary);
  text-decoration: none;
  font-weight: 500;
  transition: color 0.2s ease;
}

.auth-link:hover {
  color: #333;
  text-decoration: underline;
}

/* Loading Spinner Styles */
.spinner {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
}

.spinner ::ng-deep circle {
  stroke: var(--auth-button-text);
}

/* Material UI Button Overrides for Authentication */
.auth-button.mat-mdc-raised-button {
  background-color: var(--auth-button-bg) !important;
  color: var(--auth-button-text) !important;
}

.auth-button.mat-mdc-raised-button:hover:not(:disabled) {
  background-color: var(--auth-button-hover) !important;
}

.auth-button.mat-mdc-raised-button:disabled {
  background-color: var(--auth-button-disabled) !important;
  color: var(--auth-text-muted) !important;
}

/* Material UI Icon Button Overrides */
.auth-icon-button ::ng-deep .mat-mdc-button-touch-target {
  color: var(--auth-text-secondary) !important;
}

.auth-icon-button ::ng-deep mat-icon {
  color: var(--auth-text-secondary) !important;
}

/* Material UI Divider Overrides */
.auth-divider ::ng-deep .mat-mdc-divider {
  border-top-color: #f0f0f0 !important;
}

/* Full Width Utility Class */
.full-width {
  width: 100%;
  margin-bottom: 16px;
}

/* Button Content and Loading States */
.button-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  position: relative;
}

.button-spinner {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
}

.hidden {
  visibility: hidden;
  opacity: 0;
}

/* Back Button Styles */
.back-button {
  display: flex;
  align-items: center;
  gap: 4px;
  color: var(--auth-text-secondary) !important;
  text-transform: none;
  font-size: 14px;
  margin: 0 auto;
  background: transparent !important;
}

.back-button:hover {
  color: var(--auth-text-primary) !important;
  background: transparent !important;
}

.back-to-login {
  text-align: center;
  padding-top: 24px;
}

/* Password Strength Indicator */
.password-strength {
  margin: 16px 0;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.password-strength h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  color: var(--auth-text-primary);
  font-weight: 500;
}

.strength-requirement {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.requirement-icon {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: bold;
}

.requirement-icon.met {
  background-color: #28a745;
  color: white;
}

.requirement-icon.unmet {
  background-color: #dc3545;
  color: white;
}

.requirement-text {
  font-size: 13px;
}

.requirement-text.met {
  color: #28a745;
}

.requirement-text.unmet {
  color: #dc3545;
}

/* Resend Section Styles */
.resend-section {
  text-align: center;
  margin-bottom: 24px;
}

.resend-text {
  color: var(--auth-text-secondary);
  font-size: 14px;
  margin-bottom: 16px;
}

.resend-button {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 14px;
  text-transform: none;
  margin: 0 auto;
  position: relative;
  background: transparent !important;
}

.resend-button:not(.disabled) {
  color: var(--auth-text-secondary) !important;
}

.resend-button:not(.disabled):hover {
  color: var(--auth-text-primary) !important;
  background: transparent !important;
}

.resend-button.disabled {
  color: var(--auth-text-muted) !important;
  cursor: not-allowed;
}

.resend-spinner {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
}

/* Token Input Styles for Verify Reset Token Component */
.token-container {
  margin-bottom: 32px;
}

.token-inputs {
  display: flex;
  justify-content: space-between;
  gap: 8px;
  margin-bottom: 16px;
}

.token-input {
  width: 50px;
  height: 60px;
  border: 2px solid var(--auth-border-default);
  border-radius: 8px;
  background-color: var(--auth-bg-card);
  color: var(--auth-text-primary);
  font-size: 20px;
  font-weight: 600;
  text-align: center;
  outline: none;
  transition: all 0.2s ease;
}

.token-input:focus {
  border-color: var(--auth-border-focus);
  box-shadow: 0 0 0 3px rgba(26, 26, 26, 0.1);
}

.token-input:hover {
  border-color: var(--auth-border-hover);
}

.token-input.error {
  border-color: #dc3545;
  box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.1);
}

.token-input:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Token Error Message */
.token-error {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #dc3545;
  font-size: 14px;
  margin-top: 8px;
}

.token-error .error-icon {
  font-size: 16px;
  width: 16px;
  height: 16px;
}

/* Responsive Design */
@media (max-width: 480px) {
  .auth-container {
    padding: 16px;
  }

  .auth-card {
    padding: 32px 24px;
  }

  .auth-title {
    font-size: 24px;
  }
}
