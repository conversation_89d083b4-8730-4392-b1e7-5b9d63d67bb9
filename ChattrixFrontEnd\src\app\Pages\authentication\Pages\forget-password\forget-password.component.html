<div class="auth-container">
  <div class="auth-card">
    <!-- Header Section -->
    <div class="auth-header">
      <h1 class="auth-title">Forgot Password?</h1>
      <p class="auth-subtitle">
        Enter your email address and we'll send you a verification code to reset
        your password.
      </p>
    </div>

    <!-- Forgot Password Form -->
    <form
      [formGroup]="forgotPasswordForm"
      (ngSubmit)="onSendResetLink()"
      class="auth-form"
    >
      <!-- Email Field -->
      <mat-form-field appearance="outline" class="form-field">
        <mat-label>Email Address</mat-label>
        <input
          matInput
          type="email"
          formControlName="email"
          placeholder="Enter your email address"
          autocomplete="email"
        />
        <mat-error *ngIf="emailControl?.hasError('required')">
          Email is required
        </mat-error>
        <mat-error *ngIf="emailControl?.hasError('email')">
          Please enter a valid email address
        </mat-error>
      </mat-form-field>

      <!-- Send Reset Link Button -->
      <button
        mat-raised-button
        type="submit"
        class="auth-button full-width"
        [disabled]="forgotPasswordForm.invalid || isLoading"
      >
        <div class="button-content">
          <mat-spinner
            *ngIf="isLoading"
            diameter="20"
            class="button-spinner"
          ></mat-spinner>
          <span [class.hidden]="isLoading">Send Reset Code</span>
        </div>
      </button>
    </form>

    <!-- Footer Section -->
    <div class="auth-footer">
      <div class="back-to-login">
        <button mat-button (click)="onBackToLogin()" class="back-button">
          <mat-icon>arrow_back</mat-icon>
          Back to Login
        </button>
      </div>
    </div>
  </div>
</div>
