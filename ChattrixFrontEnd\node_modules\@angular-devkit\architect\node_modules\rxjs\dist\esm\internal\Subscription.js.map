{"version": 3, "file": "Subscription.js", "sourceRoot": "", "sources": ["../../../src/internal/Subscription.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,UAAU,EAAE,MAAM,mBAAmB,CAAC;AAC/C,OAAO,EAAE,mBAAmB,EAAE,MAAM,4BAA4B,CAAC;AAEjE,OAAO,EAAE,SAAS,EAAE,MAAM,kBAAkB,CAAC;AAc7C,MAAM,OAAO,YAAY;IAyBvB,YAAoB,eAA4B;QAA5B,oBAAe,GAAf,eAAe,CAAa;QAdzC,WAAM,GAAG,KAAK,CAAC;QAEd,eAAU,GAAyC,IAAI,CAAC;QAMxD,gBAAW,GAA0C,IAAI,CAAC;IAMf,CAAC;IAQpD,WAAW;QACT,IAAI,MAAyB,CAAC;QAE9B,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;YAChB,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;YAGnB,MAAM,EAAE,UAAU,EAAE,GAAG,IAAI,CAAC;YAC5B,IAAI,UAAU,EAAE;gBACd,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;gBACvB,IAAI,KAAK,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE;oBAC7B,KAAK,MAAM,MAAM,IAAI,UAAU,EAAE;wBAC/B,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;qBACrB;iBACF;qBAAM;oBACL,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;iBACzB;aACF;YAED,MAAM,EAAE,eAAe,EAAE,gBAAgB,EAAE,GAAG,IAAI,CAAC;YACnD,IAAI,UAAU,CAAC,gBAAgB,CAAC,EAAE;gBAChC,IAAI;oBACF,gBAAgB,EAAE,CAAC;iBACpB;gBAAC,OAAO,CAAC,EAAE;oBACV,MAAM,GAAG,CAAC,YAAY,mBAAmB,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;iBAC5D;aACF;YAED,MAAM,EAAE,WAAW,EAAE,GAAG,IAAI,CAAC;YAC7B,IAAI,WAAW,EAAE;gBACf,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;gBACxB,KAAK,MAAM,SAAS,IAAI,WAAW,EAAE;oBACnC,IAAI;wBACF,aAAa,CAAC,SAAS,CAAC,CAAC;qBAC1B;oBAAC,OAAO,GAAG,EAAE;wBACZ,MAAM,GAAG,MAAM,aAAN,MAAM,cAAN,MAAM,GAAI,EAAE,CAAC;wBACtB,IAAI,GAAG,YAAY,mBAAmB,EAAE;4BACtC,MAAM,GAAG,CAAC,GAAG,MAAM,EAAE,GAAG,GAAG,CAAC,MAAM,CAAC,CAAC;yBACrC;6BAAM;4BACL,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;yBAClB;qBACF;iBACF;aACF;YAED,IAAI,MAAM,EAAE;gBACV,MAAM,IAAI,mBAAmB,CAAC,MAAM,CAAC,CAAC;aACvC;SACF;IACH,CAAC;IAoBD,GAAG,CAAC,QAAuB;;QAGzB,IAAI,QAAQ,IAAI,QAAQ,KAAK,IAAI,EAAE;YACjC,IAAI,IAAI,CAAC,MAAM,EAAE;gBAGf,aAAa,CAAC,QAAQ,CAAC,CAAC;aACzB;iBAAM;gBACL,IAAI,QAAQ,YAAY,YAAY,EAAE;oBAGpC,IAAI,QAAQ,CAAC,MAAM,IAAI,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE;wBAChD,OAAO;qBACR;oBACD,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;iBAC3B;gBACD,CAAC,IAAI,CAAC,WAAW,GAAG,MAAA,IAAI,CAAC,WAAW,mCAAI,EAAE,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;aAC5D;SACF;IACH,CAAC;IAOO,UAAU,CAAC,MAAoB;QACrC,MAAM,EAAE,UAAU,EAAE,GAAG,IAAI,CAAC;QAC5B,OAAO,UAAU,KAAK,MAAM,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,UAAU,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC;IAC7F,CAAC;IASO,UAAU,CAAC,MAAoB;QACrC,MAAM,EAAE,UAAU,EAAE,GAAG,IAAI,CAAC;QAC5B,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,UAAU,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;IACnI,CAAC;IAMO,aAAa,CAAC,MAAoB;QACxC,MAAM,EAAE,UAAU,EAAE,GAAG,IAAI,CAAC;QAC5B,IAAI,UAAU,KAAK,MAAM,EAAE;YACzB,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;SACxB;aAAM,IAAI,KAAK,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE;YACpC,SAAS,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;SAC/B;IACH,CAAC;IAgBD,MAAM,CAAC,QAAsC;QAC3C,MAAM,EAAE,WAAW,EAAE,GAAG,IAAI,CAAC;QAC7B,WAAW,IAAI,SAAS,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC;QAEhD,IAAI,QAAQ,YAAY,YAAY,EAAE;YACpC,QAAQ,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;SAC9B;IACH,CAAC;;AAlLa,kBAAK,GAAG,CAAC,GAAG,EAAE;IAC1B,MAAM,KAAK,GAAG,IAAI,YAAY,EAAE,CAAC;IACjC,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC;IACpB,OAAO,KAAK,CAAC;AACf,CAAC,CAAC,EAAE,CAAC;AAiLP,MAAM,CAAC,MAAM,kBAAkB,GAAG,YAAY,CAAC,KAAK,CAAC;AAErD,MAAM,UAAU,cAAc,CAAC,KAAU;IACvC,OAAO,CACL,KAAK,YAAY,YAAY;QAC7B,CAAC,KAAK,IAAI,QAAQ,IAAI,KAAK,IAAI,UAAU,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,UAAU,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,CACnH,CAAC;AACJ,CAAC;AAED,SAAS,aAAa,CAAC,SAAwC;IAC7D,IAAI,UAAU,CAAC,SAAS,CAAC,EAAE;QACzB,SAAS,EAAE,CAAC;KACb;SAAM;QACL,SAAS,CAAC,WAAW,EAAE,CAAC;KACzB;AACH,CAAC"}